<template>
  <CommonPage
    :api-config="apiConfig"
    :search-config="searchConfig"
    :table-config="tableConfig"
    :pagination-config="paginationConfig"
    :initial-search-form="initialSearchForm"
    @load-initial-data="loadInitialData"
    @data-loaded="onDataLoaded"
    @custom-action="handleCustomAction"
    @show-custom-column="showCustomColumn"
    @hidden-amount-change="onHiddenAmountChange"
  >
    <!-- 自定义表格列内容 -->
    <template #column-remark2="{ row }">
      <span>{{ filterData(row.markStatus, row.remark2) }}</span>
    </template>

    <template #column-dailyDiffRatio="{ row }">
      <div>
        <span 
          v-if="row.dailyDiffRatio == 'NA' || parseFloat(row.dailyDiffRatio) > 20"
          style="color: red"
        >
          {{ row.dailyDiffRatio === 'NA' ? "-" : row.dailyDiffRatio }}
        </span>
        <span v-else>
          {{ ["0%", "0.00%"].includes(row.dailyDiffRatio) || row.dailyDiffRatio == null ? "-" : row.dailyDiffRatio }}
        </span>
      </div>
    </template>

    <!-- 自定义对话框 -->
    <template #dialogs="{ visible, form }">
      <custom-config 
        v-if="salaryVisible" 
        @success="handleSuccess" 
        :visible.sync="salaryVisible"
      />
    </template>
  </CommonPage>
</template>

<script>
import CommonPage from '@/components/CommonPage/CommonPage.vue';
import { createPageConfig } from '@/utils/pageConfigGenerator';
import { exceptionEnum, productionCategorysEnum } from '@/utils/constant';
import customConfig from './salarySearchCustom/custom-config';
import moment from 'moment';

export default {
  name: 'SalarySearchExample',
  components: {
    CommonPage,
    customConfig
  },

  data() {
    return {
      // 使用配置生成器创建页面配置
      ...createPageConfig('salaryQuery', (generator) => {
        // 自定义配置
        generator
          .setApiConfig({
            list: this.$api.softwareInformation.salarySearch.salaryQuery,
            export: this.$api.softwareInformation.salarySearch.exportSalaryQuery
          })
          .addTableColumns([
            { key: 'amount', config: { prop: 'dailyWage', label: '工序日均应发' }},
            { key: 'amount', config: { prop: 'personDailyWage', label: '个人日均应发' }},
            'dailyDiffRatio'
          ])
          .setTableConfig({
            spanConfig: {
              groupFields: ['factoryName'],
              enableCache: true
            },
            virtualConfig: {
              threshold: 100
            }
          });
      }),

      // 页面特有数据
      tabList: [],
      productionCategorysEnum,
      invalidOptions: exceptionEnum,
      salaryVisible: false,
      columnList: [],
      items: Object.freeze({
        dailyWage: {
          name: "工序日均应发",
          tips: "工序日均应发=该厂该月子工序的总应发工资/该厂该月子工序的（总出勤天数+杂工加班小时/8）",
        },
        // ... 其他提示信息
      })
    };
  },

  methods: {
    // 加载初始数据
    async loadInitialData() {
      try {
        const res = await this.$api.softwareSystemManage.getBasicPermission.getBasicPermissionAll();
        this.tabList = res.data.map((item) => ({
          label: item.name,
          name: item.name,
          id: item.id,
        })) || [];
      } catch (error) {
        console.error('加载工厂数据失败:', error);
      }
    },

    // 数据加载完成
    onDataLoaded({ list, total }) {
      // 添加序号
      this.tableData = list.map((item, index) => ({ 
        ...item, 
        serialNumber: index + 1 
      }));
    },

    // 处理自定义操作
    handleCustomAction({ action, key }) {
      switch (action) {
        case 'showCustomColumn':
          this.salaryVisible = true;
          break;
        default:
          console.log('未处理的自定义操作:', action, key);
      }
    },

    // 显示自定义列
    showCustomColumn() {
      this.salaryVisible = true;
    },

    // 隐藏金额变化
    onHiddenAmountChange(val) {
      this.hiddenAmount = val;
    },

    // 自定义列成功回调
    async handleSuccess() {
      // 重新获取列配置
      await this.getScenColumns();
    },

    // 过滤数据
    filterData(value, remark) {
      if (!["其他（暂不发放）", "其他", '正常'].includes(value)) return value;
      if (remark) {
        if (value == "其他（暂不发放）") {
          return `${value}-${remark}`;
        } else {
          return remark;
        }
      } else {
        if (value == "其他（暂不发放）") {
          return `${value}`;
        } else {
          return "";
        }
      }
    },

    // 获取场景列配置
    async getScenColumns() {
      try {
        const { data } = await this.$api.softwareWorkbench.sceneConfigurationList("SalaryQuery");
        const list = data.filter((item) => item.name === '软体工资查询');
        const scene = list && list[0] || {};

        if (scene.columns && scene.columns.length) {
          this.columnList = scene.columns;
          this.handleColumns();
        }
      } catch (error) {
        console.error('获取列配置失败:', error);
      }
    },

    // 处理列配置
    handleColumns() {
      // 列宽度处理逻辑
      let items = {
        serialNumber: "60",
        staffName: "100",
        staffCode: "120",
        idCard: "140"
      };

      this.columnList = this.columnList.map((item) => {
        if (Object.keys(items).includes(item.fieldName)) {
          item.width = items[item.fieldName];
        } else {
          item.width = this.flexWidth(
            item.fieldName,
            this.tableData,
            item.columnName
          );
        }

        if (["序号", "员工姓名", "厂牌编号"].includes(item.columnName)) {
          item.fixed = "left";
        }

        return item;
      });

      // 计算总宽度
      let totalWidth = this.columnList.reduce((pre, cur) => {
        return (pre += Number(cur.width));
      }, 0);

      if (totalWidth <= this.$refs.tableRef.$el.clientWidth) {
        this.columnList.forEach((item) => {
          delete item.width;
        });
      }
    },

    // 计算表格列宽度
    flexWidth(...args) {
      return calculateTableWidth(...args);
    }
  }
};
</script>

<style lang="stylus" scoped>
// 页面特有样式
</style>
