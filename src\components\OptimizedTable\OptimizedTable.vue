<template>
  <div class="optimized-table">
    <!-- 虚拟滚动容器 -->
    <div 
      v-if="virtualState.useVirtual"
      class="virtual-table-container"
      :style="{ height: containerHeight + 'px' }"
    >
      <!-- 虚拟滚动占位 -->
      <div :style="{ height: virtualState.totalHeight + 'px', position: 'relative' }">
        <div 
          :style="{ 
            transform: `translateY(${virtualState.offsetY}px)`,
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0
          }"
        >
          <el-table
            ref="tableRef"
            v-bind="$attrs"
            v-on="$listeners"
            :data="virtualState.visibleData"
            :span-method="optimizedSpanMethod"
            :height="null"
          >
            <slot></slot>
          </el-table>
        </div>
      </div>
    </div>

    <!-- 普通表格 -->
    <el-table
      v-else
      ref="tableRef"
      v-bind="$attrs"
      v-on="$listeners"
      :data="optimizedData"
      :span-method="optimizedSpanMethod"
    >
      <slot></slot>
    </el-table>
  </div>
</template>

<script>
import { tableSpanMixin } from '@/utils/tableSpanOptimizer';
import { virtualTableMixin, tablePerformanceUtils } from '@/utils/virtualTableOptimizer';

export default {
  name: 'OptimizedTable',
  mixins: [tableSpanMixin, virtualTableMixin],
  
  props: {
    // 表格数据
    data: {
      type: Array,
      default: () => []
    },
    // 合并配置
    spanConfig: {
      type: Object,
      default: () => ({
        groupFields: ['factoryName'],
        enableCache: true
      })
    },
    // 虚拟滚动配置
    virtualConfig: {
      type: Object,
      default: () => ({
        itemHeight: 40,
        bufferSize: 10,
        threshold: 100
      })
    },
    // 容器高度
    containerHeight: {
      type: Number,
      default: 400
    },
    // 是否启用性能优化
    enableOptimization: {
      type: Boolean,
      default: true
    }
  },

  data() {
    return {
      optimizedData: [],
      isOptimizing: false,
      performanceStats: {
        lastUpdateTime: 0,
        updateCount: 0,
        cacheHitRate: 0
      }
    };
  },

  watch: {
    data: {
      handler(newData) {
        this.optimizeTableData(newData);
      },
      immediate: true,
      deep: false // 避免深度监听大数组
    }
  },

  methods: {
    /**
     * 优化表格数据
     */
    async optimizeTableData(data) {
      if (!this.enableOptimization || this.isOptimizing) {
        this.optimizedData = data;
        return;
      }

      this.isOptimizing = true;
      const startTime = performance.now();

      try {
        // 使用批量DOM更新
        await tablePerformanceUtils.batchDOMUpdate(() => {
          // 1. 初始化虚拟表格
          this.initVirtualTable(data, this.virtualConfig);
          
          // 2. 计算表格合并
          if (this.virtualState.useVirtual) {
            this.computeTableSpan(this.virtualState.visibleData, this.spanConfig);
          } else {
            this.computeTableSpan(data, this.spanConfig);
            this.optimizedData = data;
          }
        });

        // 更新性能统计
        this.updatePerformanceStats(startTime);
        
      } catch (error) {
        console.error('表格优化失败:', error);
        this.optimizedData = data;
      } finally {
        this.isOptimizing = false;
      }
    },

    /**
     * 优化的合并方法
     */
    optimizedSpanMethod(params) {
      // 如果启用虚拟滚动，需要调整行索引
      if (this.virtualState.useVirtual) {
        const adjustedParams = {
          ...params,
          rowIndex: params.rowIndex + this.virtualState.startIndex
        };
        return this.tableSpanMethod(adjustedParams);
      }
      
      return this.tableSpanMethod(params);
    },

    /**
     * 更新性能统计
     */
    updatePerformanceStats(startTime) {
      const endTime = performance.now();
      this.performanceStats.lastUpdateTime = endTime - startTime;
      this.performanceStats.updateCount++;
      
      // 计算缓存命中率
      const cacheStats = this.spanOptimizer.getCacheStats();
      this.performanceStats.cacheHitRate = 
        cacheStats.size > 0 ? (this.performanceStats.updateCount / cacheStats.size) : 0;
    },

    /**
     * 获取性能统计信息
     */
    getPerformanceStats() {
      return {
        ...this.performanceStats,
        dataSize: this.data.length,
        visibleDataSize: this.virtualState.visibleData.length,
        useVirtual: this.virtualState.useVirtual,
        cacheSize: this.spanOptimizer.getCacheStats().size
      };
    },

    /**
     * 手动刷新表格
     */
    refresh() {
      this.optimizeTableData(this.data);
    },

    /**
     * 清理缓存
     */
    clearCache() {
      this.clearSpanCache();
      this.performanceStats.updateCount = 0;
      this.performanceStats.cacheHitRate = 0;
    },

    /**
     * 导出性能报告
     */
    exportPerformanceReport() {
      const report = {
        timestamp: new Date().toISOString(),
        stats: this.getPerformanceStats(),
        config: {
          spanConfig: this.spanConfig,
          virtualConfig: this.virtualConfig,
          enableOptimization: this.enableOptimization
        }
      };
      
      console.log('表格性能报告:', report);
      return report;
    }
  },

  beforeDestroy() {
    // 清理资源
    this.clearCache();
    
    // 清理大对象
    tablePerformanceUtils.cleanupLargeObject(this.optimizedData);
    tablePerformanceUtils.cleanupLargeObject(this.virtualState);
  }
};
</script>

<style lang="stylus" scoped>
.optimized-table {
  position: relative;
  
  .virtual-table-container {
    overflow: auto;
    position: relative;
  }
  
  // 性能优化相关样式
  .performance-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 12px;
    z-index: 1000;
    
    &.optimizing {
      background: orange;
    }
    
    &.error {
      background: red;
    }
  }
}

// 虚拟滚动优化
.virtual-table-container {
  // 硬件加速
  transform: translateZ(0);
  will-change: scroll-position;
  
  // 滚动优化
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}
</style>
