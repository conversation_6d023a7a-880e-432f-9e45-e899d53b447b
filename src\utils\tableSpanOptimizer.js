/**
 * 表格行合并性能优化工具类
 * 解决大量重复DOM操作和计算的性能问题
 */

class TableSpanOptimizer {
  constructor() {
    this.cache = new Map();
    this.lastFingerprint = '';
  }

  /**
   * 计算表格行合并配置（主入口）
   * @param {Array} data - 表格数据
   * @param {Object} options - 配置选项
   * @returns {Object} 合并配置映射
   */
  computeSpanMap(data, options = {}) {
    const {
      groupFields = ['factoryName'], // 分组字段
      enableCache = true, // 是否启用缓存
      maxCacheSize = 10 // 最大缓存数量
    } = options;

    if (!data || data.length === 0) {
      return {};
    }

    // 生成数据指纹
    const fingerprint = this.generateFingerprint(data, groupFields);
    
    // 缓存命中检查
    if (enableCache && this.cache.has(fingerprint)) {
      return this.cache.get(fingerprint);
    }

    // 计算合并配置
    const spanMap = this.calculateSpanMap(data, groupFields);

    // 缓存结果
    if (enableCache) {
      this.updateCache(fingerprint, spanMap, maxCacheSize);
    }

    return spanMap;
  }

  /**
   * 生成数据指纹用于缓存判断
   * @param {Array} data - 数据数组
   * @param {Array} fields - 影响合并的字段
   * @returns {string} 数据指纹
   */
  generateFingerprint(data, fields) {
    return data
      .map(item => fields.map(field => item[field] || '').join('-'))
      .join('|');
  }

  /**
   * 计算行合并配置
   * @param {Array} data - 表格数据
   * @param {Array} groupFields - 分组字段
   * @returns {Object} 合并配置
   */
  calculateSpanMap(data, groupFields) {
    const spanMap = {};
    const groups = this.groupData(data, groupFields);
    
    let currentIndex = 0;
    
    for (const group of groups) {
      const groupStartIndex = currentIndex;
      
      // 设置组的第一行
      spanMap[groupStartIndex] = {
        rowspan: group.length,
        isGroupStart: true,
        groupSize: group.length
      };
      
      // 设置组内其他行
      for (let i = 1; i < group.length; i++) {
        spanMap[currentIndex + i] = {
          rowspan: 0,
          isGroupStart: false,
          groupSize: 0
        };
      }
      
      currentIndex += group.length;
    }
    
    return spanMap;
  }

  /**
   * 按指定字段分组数据
   * @param {Array} data - 原始数据
   * @param {Array} fields - 分组字段
   * @returns {Array} 分组后的数据
   */
  groupData(data, fields) {
    const groups = [];
    let currentGroup = [];
    let currentGroupKey = null;
    
    for (const item of data) {
      const groupKey = fields.map(field => item[field]).join('-');
      
      if (currentGroupKey !== groupKey) {
        if (currentGroup.length > 0) {
          groups.push(currentGroup);
        }
        currentGroup = [item];
        currentGroupKey = groupKey;
      } else {
        currentGroup.push(item);
      }
    }
    
    if (currentGroup.length > 0) {
      groups.push(currentGroup);
    }
    
    return groups;
  }

  /**
   * 更新缓存
   * @param {string} fingerprint - 数据指纹
   * @param {Object} spanMap - 合并配置
   * @param {number} maxSize - 最大缓存数量
   */
  updateCache(fingerprint, spanMap, maxSize) {
    // 如果缓存已满，删除最旧的条目
    if (this.cache.size >= maxSize) {
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    
    this.cache.set(fingerprint, spanMap);
  }

  /**
   * 清空缓存
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    };
  }
}

/**
 * 表格合并Mixin - 提供给Vue组件使用
 */
export const tableSpanMixin = {
  data() {
    return {
      spanOptimizer: new TableSpanOptimizer(),
      spanMap: {},
      lastSpanFingerprint: ''
    };
  },

  methods: {
    /**
     * 计算表格合并配置
     * @param {Array} data - 表格数据
     * @param {Object} options - 配置选项
     */
    computeTableSpan(data, options = {}) {
      const defaultOptions = {
        groupFields: ['factoryName'],
        enableCache: true,
        maxCacheSize: 10
      };
      
      const mergedOptions = { ...defaultOptions, ...options };
      this.spanMap = this.spanOptimizer.computeSpanMap(data, mergedOptions);
    },

    /**
     * 表格合并方法 - 用于el-table的span-method
     * @param {Object} param - 表格参数
     * @returns {Object|undefined} 合并配置
     */
    tableSpanMethod({ rowIndex, columnIndex }) {
      const spanConfig = this.spanMap[rowIndex];
      
      if (!spanConfig) {
        return;
      }

      // 只对第一列进行合并
      if (columnIndex === 0) {
        return {
          rowspan: spanConfig.rowspan,
          colspan: 1
        };
      }
    },

    /**
     * 清空合并缓存
     */
    clearSpanCache() {
      this.spanOptimizer.clearCache();
    }
  },

  beforeDestroy() {
    // 组件销毁时清理缓存
    if (this.spanOptimizer) {
      this.spanOptimizer.clearCache();
    }
  }
};

export default TableSpanOptimizer;
