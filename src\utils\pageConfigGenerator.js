/**
 * 页面配置生成器
 * 用于快速生成CommonPage组件的配置
 */

import moment from 'moment';

/**
 * 常用搜索字段配置
 */
export const commonSearchFields = {
  // 员工姓名（带批量搜索）
  staffName: {
    prop: 'staffName',
    label: '员工姓名:',
    type: 'input',
    placeholder: '请输入员工姓名',
    batch: true,
    batchKey: 'staffNames'
  },

  // 厂牌编号（带批量搜索）
  staffCode: {
    prop: 'staffCode',
    label: '厂牌编号:',
    type: 'input',
    placeholder: '请输入厂牌编号',
    batch: true,
    batchKey: 'staffCodes'
  },

  // 工厂选择
  factoryId: {
    prop: 'factoryId',
    label: '工厂名称:',
    type: 'select',
    placeholder: '请选择工厂名称',
    filterable: true,
    autoSearch: true,
    optionsKey: 'tabList'
  },

  // 子工序
  subProcess: {
    prop: 'subProcess',
    label: '子工序:',
    type: 'input',
    placeholder: '请输入子工序'
  },

  // 员工类别
  productionCategory: {
    prop: 'productionCategory',
    label: '员工类别:',
    type: 'select',
    placeholder: '请选择',
    autoSearch: true,
    optionsKey: 'productionCategorysEnum'
  },

  // 核算月份
  accountingMonth: {
    prop: 'accountingMonth',
    label: '核算月份:',
    type: 'date',
    dateType: 'month',
    placeholder: '请选择日期',
    clearable: false,
    autoSearch: true
  },

  // 月份范围
  monthRange: {
    prop: 'monthRange',
    label: '核算月份:',
    type: 'dateRange',
    dateType: 'month',
    startProp: 'startYearMonth',
    endProp: 'endYearMonth',
    startPlaceholder: '开始月份',
    endPlaceholder: '结束月份',
    startPickerOptions: {
      disabledDate: (time) => {
        const currentMonth = moment().endOf('month').toDate();
        return time.getTime() > currentMonth.getTime();
      }
    },
    endPickerOptions: {
      disabledDate: (time) => {
        const currentMonth = moment().endOf('month').toDate();
        return time.getTime() > currentMonth.getTime();
      }
    }
  },

  // 异常类型
  invalidSearch: {
    prop: 'invalidSearch',
    label: '异常类型:',
    type: 'select',
    placeholder: '请选择',
    autoSearch: true,
    optionsKey: 'invalidOptions'
  }
};

/**
 * 常用表格列配置
 */
export const commonTableColumns = {
  // 序号
  serialNumber: {
    prop: 'serialNumber',
    label: '序号',
    width: 60,
    fixed: 'left'
  },

  // 员工姓名
  staffName: {
    prop: 'staffName',
    label: '员工姓名',
    width: 100,
    fixed: 'left'
  },

  // 厂牌编号
  staffCode: {
    prop: 'staffCode',
    label: '厂牌编号',
    width: 120,
    fixed: 'left'
  },

  // 工厂名称
  factoryName: {
    prop: 'factoryName',
    label: '工厂名称',
    width: 120
  },

  // 核算月份
  accountingMonth: {
    prop: 'accountingMonth',
    label: '核算月份',
    width: 100
  },

  // 金额列（带格式化）
  amount: (prop, label) => ({
    prop,
    label,
    width: 120,
    align: 'right',
    customRender: true,
    formatter: (row) => {
      const value = row[prop];
      return value ? `¥${Number(value).toLocaleString()}` : '-';
    }
  }),

  // 操作列
  actions: (actions = ['edit', 'delete']) => ({
    prop: 'actions',
    label: '操作',
    width: 150,
    fixed: 'right',
    customRender: true,
    formatter: (row, column) => {
      const buttons = actions.map(action => {
        const config = {
          edit: { text: '编辑', type: 'primary', size: 'mini' },
          delete: { text: '删除', type: 'danger', size: 'mini' },
          detail: { text: '详情', type: 'info', size: 'mini' }
        };
        return `<el-button ${Object.entries(config[action] || {}).map(([k, v]) => `${k}="${v}"`).join(' ')} onclick="handleRowAction('${action}', ${row.id})">${config[action]?.text || action}</el-button>`;
      });
      return buttons.join(' ');
    }
  })
};

/**
 * 页面配置生成器类
 */
export class PageConfigGenerator {
  constructor() {
    this.config = {
      apiConfig: {},
      searchConfig: {
        fields: [],
        customButtons: [],
        labelPosition: 'right'
      },
      tableConfig: {
        columns: [],
        showExport: true,
        showAdd: false,
        showCustomColumn: false,
        showHiddenAmountOption: false,
        customButtons: []
      },
      paginationConfig: {
        pageSizes: [50, 100, 200],
        layout: 'total, sizes, prev, pager, next, jumper'
      },
      initialSearchForm: {}
    };
  }

  /**
   * 设置API配置
   */
  setApiConfig(apiConfig) {
    this.config.apiConfig = { ...this.config.apiConfig, ...apiConfig };
    return this;
  }

  /**
   * 添加搜索字段
   */
  addSearchField(fieldKey, customConfig = {}) {
    const baseConfig = commonSearchFields[fieldKey];
    if (!baseConfig) {
      console.warn(`未找到搜索字段配置: ${fieldKey}`);
      return this;
    }

    const fieldConfig = { ...baseConfig, ...customConfig };
    this.config.searchConfig.fields.push(fieldConfig);

    // 设置初始值
    if (fieldConfig.defaultValue !== undefined) {
      this.config.initialSearchForm[fieldConfig.prop] = fieldConfig.defaultValue;
    }

    return this;
  }

  /**
   * 添加多个搜索字段
   */
  addSearchFields(fields) {
    fields.forEach(field => {
      if (typeof field === 'string') {
        this.addSearchField(field);
      } else {
        this.addSearchField(field.key, field.config);
      }
    });
    return this;
  }

  /**
   * 添加表格列
   */
  addTableColumn(columnKey, customConfig = {}) {
    const baseConfig = commonTableColumns[columnKey];
    if (!baseConfig) {
      console.warn(`未找到表格列配置: ${columnKey}`);
      return this;
    }

    let columnConfig;
    if (typeof baseConfig === 'function') {
      columnConfig = baseConfig(customConfig.prop, customConfig.label);
    } else {
      columnConfig = { ...baseConfig, ...customConfig };
    }

    this.config.tableConfig.columns.push(columnConfig);
    return this;
  }

  /**
   * 添加多个表格列
   */
  addTableColumns(columns) {
    columns.forEach(column => {
      if (typeof column === 'string') {
        this.addTableColumn(column);
      } else {
        this.addTableColumn(column.key, column.config);
      }
    });
    return this;
  }

  /**
   * 设置表格配置
   */
  setTableConfig(tableConfig) {
    this.config.tableConfig = { ...this.config.tableConfig, ...tableConfig };
    return this;
  }

  /**
   * 添加自定义搜索按钮
   */
  addSearchButton(button) {
    this.config.searchConfig.customButtons.push(button);
    return this;
  }

  /**
   * 添加自定义表格按钮
   */
  addTableButton(button) {
    this.config.tableConfig.customButtons.push(button);
    return this;
  }

  /**
   * 设置分页配置
   */
  setPaginationConfig(paginationConfig) {
    this.config.paginationConfig = { ...this.config.paginationConfig, ...paginationConfig };
    return this;
  }

  /**
   * 生成最终配置
   */
  build() {
    return { ...this.config };
  }
}

/**
 * 预设配置模板
 */
export const pageTemplates = {
  // 员工管理页面模板
  employeeManagement: () => {
    return new PageConfigGenerator()
      .addSearchFields(['staffName', 'staffCode', 'factoryId'])
      .addTableColumns(['serialNumber', 'staffName', 'staffCode', 'factoryName'])
      .addTableColumn('actions', { actions: ['edit', 'delete'] })
      .setTableConfig({
        showAdd: true,
        showExport: true
      });
  },

  // 工资查询页面模板
  salaryQuery: () => {
    return new PageConfigGenerator()
      .addSearchFields([
        'staffName', 
        'staffCode', 
        'factoryId', 
        'subProcess', 
        'productionCategory',
        { key: 'monthRange', config: { 
          defaultValue: moment().subtract(1, 'months').format('YYYY-MM') 
        }},
        'invalidSearch'
      ])
      .addTableColumns(['serialNumber', 'staffName', 'staffCode', 'factoryName', 'accountingMonth'])
      .setTableConfig({
        showExport: true,
        showCustomColumn: true,
        showHiddenAmountOption: true
      });
  },

  // 数据上传页面模板
  dataUpload: () => {
    return new PageConfigGenerator()
      .addSearchFields(['staffName', 'staffCode'])
      .addTableColumns(['serialNumber', 'staffName', 'staffCode'])
      .addTableColumn('actions', { actions: ['edit', 'delete'] })
      .setTableConfig({
        showAdd: true,
        showExport: true
      })
      .addTableButton({
        key: 'import',
        text: '导入',
        type: 'success',
        action: 'import'
      });
  },

  // 报表页面模板
  report: () => {
    return new PageConfigGenerator()
      .addSearchFields(['factoryId', 'accountingMonth'])
      .addTableColumns(['serialNumber', 'factoryName', 'accountingMonth'])
      .setTableConfig({
        showExport: true
      });
  }
};

/**
 * 快速创建页面配置
 */
export function createPageConfig(template, customizer) {
  const generator = pageTemplates[template] ? pageTemplates[template]() : new PageConfigGenerator();
  
  if (customizer && typeof customizer === 'function') {
    customizer(generator);
  }
  
  return generator.build();
}

export default PageConfigGenerator;
