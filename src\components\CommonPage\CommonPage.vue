<template>
  <content-panel>
    <!-- 搜索区域 -->
    <template v-slot:search>
      <search-box class="search-box">
        <el-form 
          size="mini" 
          :inline="true" 
          :model="searchForm" 
          ref="searchForm"
          :label-position="searchConfig.labelPosition || 'right'"
        >
          <!-- 动态渲染搜索字段 -->
          <el-form-item 
            v-for="field in searchConfig.fields" 
            :key="field.prop"
            :label="field.label" 
            :prop="field.prop"
          >
            <!-- 输入框 -->
            <el-input
              v-if="field.type === 'input'"
              v-model.trim="searchForm[field.prop]"
              :placeholder="field.placeholder"
              size="mini"
              clearable
              @keyup.enter.native="onSearch"
            >
              <!-- 批量搜索 -->
              <template v-if="field.batch" slot="append">
                <search-batch
                  @seachFilter="onSearch(field.batchKey, $event)"
                  @focusEvent="focusEvent(field.batchKey, $event)"
                  :ref="`children${field.batchKey}`"
                  :titleName="field.label"
                />
              </template>
            </el-input>

            <!-- 选择器 -->
            <el-select
              v-else-if="field.type === 'select'"
              v-model="searchForm[field.prop]"
              :placeholder="field.placeholder"
              :filterable="field.filterable"
              clearable
              @change="field.autoSearch ? onSearch() : null"
            >
              <el-option
                v-for="option in getFieldOptions(field)"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>

            <!-- 日期选择器 -->
            <el-date-picker
              v-else-if="field.type === 'date'"
              v-model="searchForm[field.prop]"
              :type="field.dateType || 'month'"
              :placeholder="field.placeholder"
              :clearable="field.clearable !== false"
              :picker-options="field.pickerOptions"
              @change="field.autoSearch ? onSearch() : null"
            />

            <!-- 日期范围 -->
            <template v-else-if="field.type === 'dateRange'">
              <el-date-picker
                v-model="searchForm[field.startProp]"
                :type="field.dateType || 'month'"
                :placeholder="field.startPlaceholder"
                :clearable="false"
                :picker-options="field.startPickerOptions"
              />
              <span class="separator">至</span>
              <el-date-picker
                v-model="searchForm[field.endProp]"
                :type="field.dateType || 'month'"
                :placeholder="field.endPlaceholder"
                :clearable="false"
                :picker-options="field.endPickerOptions"
              />
            </template>
          </el-form-item>
        </el-form>

        <!-- 操作按钮 -->
        <template v-slot:right>
          <el-button size="small" type="primary" @click="onSearch">
            查询
          </el-button>
          <el-button size="small" type="warning" @click="resetSearchForm">
            重置
          </el-button>
          <!-- 自定义按钮 -->
          <el-button
            v-for="btn in searchConfig.customButtons"
            :key="btn.key"
            :size="btn.size || 'small'"
            :type="btn.type || 'default'"
            @click="handleCustomAction(btn.action, btn.key)"
          >
            {{ btn.text }}
          </el-button>
        </template>
      </search-box>
    </template>

    <!-- 表格区域 -->
    <table-panel ref="tablePanel">
      <!-- 表格头部左侧 -->
      <template v-if="tableConfig.headerLeft" v-slot:header-left>
        <slot name="header-left" :data="tableData">
          <div v-html="tableConfig.headerLeft"></div>
        </slot>
      </template>

      <!-- 表格头部右侧 -->
      <template v-slot:header-right>
        <slot name="header-right" :data="tableData">
          <!-- 默认操作按钮 -->
          <div class="header-right-buttons">
            <!-- 隐藏金额为0的列 -->
            <el-checkbox 
              v-if="tableConfig.showHiddenAmountOption"
              style="color: #0bb78e; margin-right: 10px;" 
              v-model="hiddenAmount"
              @change="changeHiddenAmount"
            >
              导出时,隐藏金额为0的列
            </el-checkbox>

            <!-- 自定义列按钮 -->
            <el-button 
              v-if="tableConfig.showCustomColumn"
              size="small" 
              type="primary" 
              @click="showCustomColumn"
            >
              自定义列
            </el-button>

            <!-- 导出按钮 -->
            <el-button 
              v-if="tableConfig.showExport"
              size="small" 
              type="primary" 
              @click="handleExport"
            >
              导出
            </el-button>

            <!-- 新增按钮 -->
            <el-button 
              v-if="tableConfig.showAdd"
              size="small" 
              type="primary" 
              @click="handleAdd"
            >
              新增
            </el-button>

            <!-- 自定义按钮 -->
            <el-button
              v-for="btn in tableConfig.customButtons"
              :key="btn.key"
              :size="btn.size || 'small'"
              :type="btn.type || 'primary'"
              @click="handleCustomAction(btn.action, btn.key)"
            >
              {{ btn.text }}
            </el-button>
          </div>
        </slot>
      </template>

      <!-- 表格内容 -->
      <slot name="table" :data="tableData" :loading="loading">
        <!-- 默认使用 OptimizedTable -->
        <OptimizedTable
          :data="tableData"
          :loading="loading"
          :span-config="tableConfig.spanConfig"
          :virtual-config="tableConfig.virtualConfig"
          :container-height="maxTableHeight"
          v-bind="tableConfig.tableProps"
          v-on="tableConfig.tableEvents"
        >
          <!-- 动态列 -->
          <el-table-column
            v-for="column in tableConfig.columns"
            :key="column.prop"
            v-bind="column"
          >
            <!-- 自定义列内容 -->
            <template v-if="column.customRender" slot-scope="scope">
              <slot 
                :name="`column-${column.prop}`" 
                :row="scope.row" 
                :column="column" 
                :index="scope.$index"
              >
                <span v-html="renderColumnContent(scope.row, column)"></span>
              </slot>
            </template>
          </el-table-column>
        </OptimizedTable>
      </slot>

      <!-- 分页 -->
      <template v-slot:footer>
        <div style="text-align: right">
          <el-pagination
            @size-change="onSizeChange"
            @current-change="onNumChange"
            :current-page="pageNum"
            :page-size="pageSize"
            :total="total"
            :page-sizes="paginationConfig.pageSizes || [50, 100, 200]"
            :layout="paginationConfig.layout || 'total, sizes, prev, pager, next, jumper'"
          />
        </div>
      </template>
    </table-panel>

    <!-- 自定义对话框 -->
    <slot name="dialogs" :visible="dialogVisible" :form="editForm">
      <!-- 默认编辑对话框 -->
      <el-dialog
        v-if="dialogVisible"
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="600px"
      >
        <slot name="dialog-content" :form="editForm">
          <!-- 默认表单内容 -->
        </slot>
        <div slot="footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleDialogConfirm">确定</el-button>
        </div>
      </el-dialog>
    </slot>
  </content-panel>
</template>

<script>
import tableMixin from '@/utils/tableMixin';
import pagePathMixin from '@/utils/page-path-mixin';
import OptimizedTable from '@/components/OptimizedTable/OptimizedTable.vue';

export default {
  name: 'CommonPage',
  mixins: [tableMixin, pagePathMixin],
  components: { OptimizedTable },

  props: {
    // API配置
    apiConfig: {
      type: Object,
      required: true,
      default: () => ({
        list: null,        // 列表API
        export: null,      // 导出API
        add: null,         // 新增API
        update: null,      // 更新API
        delete: null,      // 删除API
        detail: null       // 详情API
      })
    },

    // 搜索配置
    searchConfig: {
      type: Object,
      default: () => ({
        fields: [],
        customButtons: [],
        labelPosition: 'right'
      })
    },

    // 表格配置
    tableConfig: {
      type: Object,
      default: () => ({
        columns: [],
        showExport: true,
        showAdd: false,
        showCustomColumn: false,
        showHiddenAmountOption: false,
        customButtons: [],
        spanConfig: {},
        virtualConfig: {},
        tableProps: {},
        tableEvents: {}
      })
    },

    // 分页配置
    paginationConfig: {
      type: Object,
      default: () => ({
        pageSizes: [50, 100, 200],
        layout: 'total, sizes, prev, pager, next, jumper'
      })
    },

    // 初始搜索表单
    initialSearchForm: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      searchForm: { ...this.initialSearchForm },
      tableData: [],
      loading: false,
      pageNum: 1,
      pageSize: 50,
      total: 0,
      filterParam: {},
      params: {},
      hiddenAmount: false,
      dialogVisible: false,
      dialogTitle: '',
      editForm: {},
      resizeOffset: 55
    };
  },

  created() {
    this.initPage();
  },

  methods: {
    // 初始化页面
    async initPage() {
      await this.loadInitialData();
      this.onSearch();
    },

    // 加载初始数据（如下拉选项等）
    async loadInitialData() {
      // 子组件可以重写此方法
      this.$emit('load-initial-data');
    },

    // 搜索
    onSearch(name, data) {
      this.buildFilterParams(name, data);
      this.pageNum = 1;
      this.getList();
    },

    // 构建过滤参数
    buildFilterParams(name, data) {
      this.filterParam = {};
      
      for (const [key, val] of Object.entries(this.searchForm)) {
        if (typeof val !== "undefined" && val !== null && val !== "") {
          this.filterParam[key] = val;
        }
      }

      if (name && data && Array.isArray(data) && data.length > 0) {
        this.filterParam[name] = data;
      }
    },

    // 获取列表数据
    async getList() {
      if (!this.apiConfig.list) {
        console.warn('未配置列表API');
        return;
      }

      this.loading = true;
      
      try {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
          filterData: {
            ...this.filterParam,
            ...this.params
          }
        };

        const response = await this.apiConfig.list(params);
        const { list, total } = response.data || {};
        
        this.tableData = list || [];
        this.total = total || 0;
        
        this.$emit('data-loaded', { list: this.tableData, total: this.total });
        
      } catch (error) {
        console.error('获取列表数据失败:', error);
        this.$message.error('获取数据失败，请重试');
      } finally {
        this.loading = false;
      }
    },

    // 重置搜索表单
    resetSearchForm() {
      this.$refs.searchForm.resetFields();
      
      // 重置批量搜索组件
      this.searchConfig.fields.forEach(field => {
        if (field.batch && this.$refs[`children${field.batchKey}`]) {
          this.$refs[`children${field.batchKey}`][0].resetFilter();
        }
      });

      this.filterParam = {};
      this.params = {};
      this.searchForm = { ...this.initialSearchForm };
      this.onSearch();
    },

    // 获取字段选项
    getFieldOptions(field) {
      if (field.options) {
        return field.options;
      }
      if (field.optionsKey && this[field.optionsKey]) {
        return this[field.optionsKey];
      }
      return [];
    },

    // 渲染列内容
    renderColumnContent(row, column) {
      if (column.formatter) {
        return column.formatter(row, column);
      }
      return row[column.prop] || '';
    },

    // 处理自定义操作
    handleCustomAction(action, key) {
      this.$emit('custom-action', { action, key });
    },

    // 导出
    handleExport() {
      if (!this.apiConfig.export) {
        console.warn('未配置导出API');
        return;
      }

      const params = {
        ...this.filterParam,
        ...this.params,
        isFilterEmpty: this.hiddenAmount ? 1 : 0
      };

      this.apiConfig.export(params).then(() => {
        this.$message.success('导出操作成功，请前往导出记录查看详情');
      });
    },

    // 新增
    handleAdd() {
      this.dialogTitle = '新增';
      this.editForm = {};
      this.dialogVisible = true;
      this.$emit('add-click');
    },

    // 显示自定义列
    showCustomColumn() {
      this.$emit('show-custom-column');
    },

    // 改变隐藏金额选项
    changeHiddenAmount(val) {
      this.hiddenAmount = val;
      this.$emit('hidden-amount-change', val);
    },

    // 对话框确认
    handleDialogConfirm() {
      this.$emit('dialog-confirm', this.editForm);
    },

    // 焦点事件
    focusEvent(name, data) {
      if (Array.isArray(data) && !data.length) {
        this.params[name] = [];
      }
      if (name && data && Array.isArray(data) && data.length > 0) {
        this.params[name] = data;
      }
    },

    // 分页
    onSizeChange(val) {
      this.pageSize = val;
      this.pageNum = 1;
      this.getList();
    },

    onNumChange(val) {
      this.pageNum = val;
      this.getList();
    }
  }
};
</script>

<style lang="stylus" scoped>
.header-right-buttons {
  display: flex;
  align-items: center;
  gap: 10px;
}

.separator {
  margin: 0 10px;
  color: #606266;
}
</style>
