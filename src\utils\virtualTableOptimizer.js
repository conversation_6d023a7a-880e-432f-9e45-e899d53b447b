/**
 * 虚拟表格优化器
 * 解决大数据量表格的性能问题
 */

import { debounce, throttle } from 'lodash-es';

class VirtualTableOptimizer {
  constructor(options = {}) {
    this.options = {
      itemHeight: 40, // 每行高度
      bufferSize: 10, // 缓冲区大小
      threshold: 100, // 启用虚拟滚动的阈值
      ...options
    };
    
    this.scrollTop = 0;
    this.containerHeight = 0;
    this.totalHeight = 0;
    this.visibleData = [];
    this.startIndex = 0;
    this.endIndex = 0;
    
    // 防抖和节流函数
    this.debouncedUpdate = debounce(this.updateVisibleData.bind(this), 16);
    this.throttledScroll = throttle(this.handleScroll.bind(this), 16);
  }

  /**
   * 初始化虚拟表格
   * @param {Array} data - 完整数据
   * @param {number} containerHeight - 容器高度
   */
  init(data, containerHeight) {
    this.data = data || [];
    this.containerHeight = containerHeight;
    this.totalHeight = this.data.length * this.options.itemHeight;
    
    // 如果数据量小于阈值，不启用虚拟滚动
    if (this.data.length < this.options.threshold) {
      this.visibleData = this.data;
      return {
        useVirtual: false,
        visibleData: this.data,
        totalHeight: this.totalHeight,
        offsetY: 0
      };
    }
    
    this.updateVisibleData();
    
    return {
      useVirtual: true,
      visibleData: this.visibleData,
      totalHeight: this.totalHeight,
      offsetY: this.startIndex * this.options.itemHeight
    };
  }

  /**
   * 处理滚动事件
   * @param {number} scrollTop - 滚动位置
   */
  handleScroll(scrollTop) {
    this.scrollTop = scrollTop;
    this.debouncedUpdate();
  }

  /**
   * 更新可见数据
   */
  updateVisibleData() {
    const visibleCount = Math.ceil(this.containerHeight / this.options.itemHeight);
    
    this.startIndex = Math.max(0, 
      Math.floor(this.scrollTop / this.options.itemHeight) - this.options.bufferSize
    );
    
    this.endIndex = Math.min(this.data.length - 1,
      this.startIndex + visibleCount + this.options.bufferSize * 2
    );
    
    this.visibleData = this.data.slice(this.startIndex, this.endIndex + 1);
    
    return {
      visibleData: this.visibleData,
      offsetY: this.startIndex * this.options.itemHeight,
      startIndex: this.startIndex,
      endIndex: this.endIndex
    };
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      visibleData: this.visibleData,
      totalHeight: this.totalHeight,
      offsetY: this.startIndex * this.options.itemHeight,
      startIndex: this.startIndex,
      endIndex: this.endIndex,
      useVirtual: this.data.length >= this.options.threshold
    };
  }

  /**
   * 销毁优化器
   */
  destroy() {
    this.debouncedUpdate.cancel();
    this.throttledScroll.cancel();
  }
}

/**
 * 虚拟表格Mixin
 */
export const virtualTableMixin = {
  data() {
    return {
      virtualOptimizer: null,
      virtualState: {
        useVirtual: false,
        visibleData: [],
        totalHeight: 0,
        offsetY: 0
      }
    };
  },

  methods: {
    /**
     * 初始化虚拟表格
     * @param {Array} data - 表格数据
     * @param {Object} options - 配置选项
     */
    initVirtualTable(data, options = {}) {
      if (!this.virtualOptimizer) {
        this.virtualOptimizer = new VirtualTableOptimizer(options);
      }
      
      const containerHeight = this.getTableContainerHeight();
      const state = this.virtualOptimizer.init(data, containerHeight);
      
      this.virtualState = state;
      
      // 如果启用虚拟滚动，设置滚动监听
      if (state.useVirtual) {
        this.$nextTick(() => {
          this.setupVirtualScroll();
        });
      }
    },

    /**
     * 获取表格容器高度
     */
    getTableContainerHeight() {
      if (this.$refs.tableRef && this.$refs.tableRef.$el) {
        return this.$refs.tableRef.$el.clientHeight || 400;
      }
      return this.maxTableHeight || 400;
    },

    /**
     * 设置虚拟滚动监听
     */
    setupVirtualScroll() {
      const tableBody = this.$refs.tableRef?.$el?.querySelector('.el-table__body-wrapper');
      
      if (tableBody) {
        tableBody.addEventListener('scroll', this.handleVirtualScroll);
      }
    },

    /**
     * 处理虚拟滚动
     * @param {Event} event - 滚动事件
     */
    handleVirtualScroll(event) {
      if (this.virtualOptimizer) {
        this.virtualOptimizer.handleScroll(event.target.scrollTop);
        
        // 更新状态
        this.virtualState = {
          ...this.virtualState,
          ...this.virtualOptimizer.getState()
        };
      }
    },

    /**
     * 清理虚拟滚动
     */
    cleanupVirtualScroll() {
      const tableBody = this.$refs.tableRef?.$el?.querySelector('.el-table__body-wrapper');
      
      if (tableBody) {
        tableBody.removeEventListener('scroll', this.handleVirtualScroll);
      }
    }
  },

  beforeDestroy() {
    this.cleanupVirtualScroll();
    
    if (this.virtualOptimizer) {
      this.virtualOptimizer.destroy();
    }
  }
};

/**
 * 表格性能优化工具函数
 */
export const tablePerformanceUtils = {
  /**
   * 防抖搜索
   * @param {Function} searchFn - 搜索函数
   * @param {number} delay - 延迟时间
   */
  createDebouncedSearch(searchFn, delay = 300) {
    return debounce(searchFn, delay);
  },

  /**
   * 节流滚动
   * @param {Function} scrollFn - 滚动函数
   * @param {number} delay - 延迟时间
   */
  createThrottledScroll(scrollFn, delay = 16) {
    return throttle(scrollFn, delay);
  },

  /**
   * 批量DOM更新
   * @param {Function} updateFn - 更新函数
   */
  batchDOMUpdate(updateFn) {
    return new Promise(resolve => {
      requestAnimationFrame(() => {
        updateFn();
        resolve();
      });
    });
  },

  /**
   * 内存优化 - 清理大对象
   * @param {Object} obj - 要清理的对象
   */
  cleanupLargeObject(obj) {
    if (obj && typeof obj === 'object') {
      Object.keys(obj).forEach(key => {
        if (Array.isArray(obj[key]) && obj[key].length > 1000) {
          obj[key] = [];
        } else if (typeof obj[key] === 'object' && obj[key] !== null) {
          obj[key] = null;
        }
      });
    }
  }
};

export default VirtualTableOptimizer;
